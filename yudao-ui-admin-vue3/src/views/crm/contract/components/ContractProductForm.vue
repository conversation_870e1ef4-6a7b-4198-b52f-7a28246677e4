<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" align="center" width="60" />
      
      <!-- 产品编码搜索 -->
      <el-table-column label="产品编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              remote
              reserve-keyword
              :remote-method="searchProducts"
              :loading="searchLoading"
              @change="onChangeProduct($event, row)"
              placeholder="请输入产品编码搜索"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="`${item.no} - ${item.name}`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      
      <!-- 品名 -->
      <el-table-column label="品名" min-width="120">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productName" />
          </el-form-item>
        </template>
      </el-table-column>
      
      <!-- 规格/颜色 -->
      <el-table-column label="规格/颜色" min-width="120">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productSpecification" />
          </el-form-item>
        </template>
      </el-table-column>
      
      <!-- 有效宽幅 -->
      <el-table-column label="有效宽幅" min-width="100">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productEffectiveWidth" />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- 原单价（CNY/USD） -->
      <el-table-column label="原单价（CNY/USD）" min-width="160">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-input disabled v-model="row.productPrice" :formatter="erpPriceInputFormatter" />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- 单价（CNY/USD） -->
      <el-table-column label="单价（CNY/USD）" fixed="right" min-width="160">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.contractPrice`" class="mb-0px!">
            <el-input-number
              v-model="row.contractPrice"
              controls-position="right"
              :min="0.001"
              :precision="2"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- 单位 -->
      <el-table-column label="单位" fixed="right" min-width="60">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <dict-tag :type="DICT_TYPE.CRM_PRODUCT_UNIT" :value="row.productUnit" />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- 数量 -->
      <el-table-column label="数量" prop="count" fixed="right" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.count`" :rules="formRules.count" class="mb-0px!">
            <el-input-number
              v-model="row.count"
              controls-position="right"
              :min="1"
              :precision="0"
              :step="1"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      
      <!-- 合计（元） -->
      <el-table-column label="合计（元）" prop="totalPrice" fixed="right" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalPrice`" class="mb-0px!">
            <el-input disabled v-model="row.totalPrice" :formatter="erpPriceInputFormatter" />
          </el-form-item>
        </template>
      </el-table-column>
      
      <!-- 操作 -->
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加产品</el-button>
  </el-row>
</template>
<script setup lang="ts">
import * as ProductApi from '@/api/crm/product'
import { erpPriceInputFormatter, erpPriceMultiply } from '@/utils'
import { DICT_TYPE } from '@/utils/dict'

const props = defineProps<{
  products: undefined
  disabled: false
}>()
const formLoading = ref(false) // 表单的加载中
const searchLoading = ref(false) // 搜索加载中
const formData = ref([])
const formRules = reactive({
  productId: [{ required: true, message: '产品不能为空', trigger: 'blur' }],
  contractPrice: [{ required: true, message: '合同价格不能为空', trigger: 'blur' }],
  count: [{ required: true, message: '产品数量不能为空', trigger: 'blur' }]
})
const formRef = ref([]) // 表单 Ref
const productList = ref<ProductApi.ProductVO[]>([]) // 产品列表

/** 初始化设置产品项 */
watch(
  () => props.products,
  async (val) => {
    formData.value = val
  },
  { immediate: true }
)

/** 监听合同产品变化，计算合同产品总价 */
watch(
  () => formData.value,
  (val) => {
    if (!val || val.length === 0) {
      return
    }
    // 循环处理
    val.forEach((item) => {
      if (item.contractPrice != null && item.count != null) {
        item.totalPrice = erpPriceMultiply(item.contractPrice, item.count)
      } else {
        item.totalPrice = undefined
      }
    })
  },
  { deep: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    productId: undefined,
    productName: undefined, // 产品名称
    productSpecification: undefined, // 产品规格/颜色
    productEffectiveWidth: undefined, // 产品有效宽幅
    productUnit: undefined, // 产品单位
    productNo: undefined, // 产品条码
    productPrice: undefined, // 产品原价格
    contractPrice: undefined, // 合同价格
    count: 1
  }
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 处理产品变更 */
const onChangeProduct = (productId, row) => {
  const product = productList.value.find((item) => item.id === productId)
  if (product) {
    row.productName = product.name
    row.productSpecification = product.specification || '-'
    row.productEffectiveWidth = product.effectiveWidth || '-'
    row.productUnit = product.unit
    row.productNo = product.no
    row.productPrice = product.price
    row.contractPrice = product.price // 默认合同价格等于原价格
  }
}

/** 搜索产品 */
const searchProducts = async (query: string) => {
  try {
    searchLoading.value = true
    if (!query || query.trim() === '') {
      // 如果查询为空，使用简单列表接口
      const data = await ProductApi.getProductSimpleList()
      productList.value = data || []
    } else {
      // 使用新的搜索接口
      const data = await ProductApi.searchProducts(query.trim())
      productList.value = data || []
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
    // 如果搜索失败，回退到简单列表
    try {
      const data = await ProductApi.getProductSimpleList()
      productList.value = data || []
    } catch (fallbackError) {
      console.error('回退加载产品列表也失败:', fallbackError)
      productList.value = []
    }
  } finally {
    searchLoading.value = false
  }
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}
defineExpose({ validate })

/** 初始化 */
onMounted(async () => {
  // 初始化时加载默认产品列表
  await searchProducts('')
})
</script>
