<template>
  <ContentWrap>
    <el-table :data="contract.products" :stripe="true" :show-overflow-tooltip="true">
      <!-- 产品编码 -->
      <el-table-column
        label="产品编码"
        align="center"
        prop="productNo"
        fixed="left"
        min-width="120"
      />
      
      <!-- 品名 -->
      <el-table-column
        align="center"
        label="品名"
        prop="productName"
        min-width="160"
      >
        <template #default="scope">
          {{ scope.row.productName }}
        </template>
      </el-table-column>
      
      <!-- 规格/颜色 -->
      <el-table-column
        label="规格/颜色"
        align="center"
        prop="productSpecification"
        min-width="120"
      >
        <template #default="{ row }">
          {{ row.productSpecification || '-' }}
        </template>
      </el-table-column>
      
      <!-- 有效宽幅 -->
      <el-table-column
        label="有效宽幅"
        align="center"
        prop="productEffectiveWidth"
        min-width="100"
      >
        <template #default="{ row }">
          {{ row.productEffectiveWidth || '-' }}
        </template>
      </el-table-column>

      <!-- 原单价（CNY/USD） -->
      <el-table-column
        label="原单价（CNY/USD）"
        align="center"
        prop="productPrice"
        min-width="140"
        :formatter="erpPriceTableColumnFormatter"
      />

      <!-- 合同单价（CNY/USD） -->
      <el-table-column
        label="合同单价（CNY/USD）"
        align="center"
        prop="contractPrice"
        min-width="140"
        :formatter="erpPriceTableColumnFormatter"
      />
      
      <!-- 单位 -->
      <el-table-column
        label="单位"
        align="center"
        prop="productUnit"
        min-width="60"
      >
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.CRM_PRODUCT_UNIT" :value="row.productUnit" />
        </template>
      </el-table-column>

      <!-- 数量 -->
      <el-table-column
        align="center"
        label="数量"
        prop="count"
        min-width="100px"
        :formatter="erpPriceTableColumnFormatter"
      />
      
      <!-- 合计金额（CNY/USD） -->
      <el-table-column
        label="合计金额（CNY/USD）"
        align="center"
        prop="totalPrice"
        min-width="140"
        :formatter="erpPriceTableColumnFormatter"
      />
    </el-table>
    <el-row class="mt-10px" justify="end">
      <el-col :span="3"> 整单折扣：{{ erpPriceInputFormatter(contract.discountPercent) }}% </el-col>
      <el-col :span="4">
        产品总金额：{{ erpPriceInputFormatter(contract.totalProductPrice) }} CNY/USD
      </el-col>
    </el-row>
  </ContentWrap>
</template>
<script setup lang="ts">
import * as ContractApi from '@/api/crm/contract'
import { erpPriceInputFormatter, erpPriceTableColumnFormatter } from '@/utils'
import { DICT_TYPE } from '@/utils/dict'

const { contract } = defineProps<{
  contract: ContractApi.ContractVO
}>()
</script>
