<template>
  <ContentWrap>
    <div class="mb-10px">
      <el-button
        type="primary"
        plain
        @click="openForm('create')"
        v-hasPermi="['crm:settlement-record:create']"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 新增结算单
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['crm:settlement-record:export']"
        class="ml-10px"
      >
        <Icon icon="ep:download" class="mr-5px" /> 导出结算单
      </el-button>
    </div>

    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="发货日期" align="center" prop="settlementDate" width="130">
        <template #default="scope">
          {{ formatDate(scope.row.settlementDate) }}
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="ownerUserName" width="100" />
      <el-table-column label="品名/款号" align="center" prop="productNameCode" width="120" />
      <el-table-column label="成分" align="center" prop="composition" width="100" />
      <el-table-column label="规格" align="center" prop="specification" width="100" />
      <el-table-column label="克重" align="center" prop="weight" width="80" />
      <el-table-column label="幅宽" align="center" prop="width" width="80" />
      <el-table-column label="数量" align="center" prop="quantity" width="80" />
      <el-table-column label="单位" align="center" prop="unit" width="60">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CRM_PRODUCT_UNIT" :value="scope.row.unit" />
        </template>
      </el-table-column>
      <el-table-column label="单价（CNY/USD）" align="center" prop="unitPrice" width="160" />
      <el-table-column label="金额（CNY/USD）" align="center" prop="totalAmount" width="160" />
      <el-table-column label="备注" align="center" prop="remark" width="120" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['crm:settlement-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['crm:settlement-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 合计金额行 -->
    <el-table :data="[{}]" :show-header="false" class="mt-0">
      <el-table-column />
      <el-table-column width="300" align="right">
        <template #default>
          <div class="flex items-center justify-end gap-2">
            <span class="text-sm font-medium text-gray-700">结算单总金额：</span>
            <span class="text-lg font-bold text-red-600">￥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column />
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      class="mt-15px"
    />

    <!-- 表单弹窗：添加/修改 -->
    <SettlementRecordForm ref="formRef" @success="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { SettlementRecordApi, SettlementRecordVO } from '@/api/crm/settlementrecord'
import SettlementRecordForm from '@/views/crm/settlementrecord/SettlementRecordForm.vue'
import * as ContractApi from '@/api/crm/contract'
import download from '@/utils/download'

/** 合同结算单列表 */
defineOptions({ name: 'ContractSettlementList' })

const props = defineProps<{
  contractId: number
}>()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<SettlementRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const contract = ref<ContractApi.ContractVO>() // 合同信息
const exportLoading = ref(false) // 导出的加载中

// 计算当前页面的总金额
const totalAmount = computed(() => {
  return list.value.reduce((sum, item) => {
    return sum + (item.totalAmount || 0)
  }, 0)
})

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  contractId: undefined as number | undefined,
  // 在合同详情页面中，显示该合同的所有结算单，不受权限限制
})

/** 格式化日期为中文格式 */
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

/** 获取合同信息 */
const getContractInfo = async () => {
  if (!props.contractId) return
  try {
    contract.value = await ContractApi.getContract(props.contractId)
  } catch (error) {
    console.error('获取合同信息失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.contractId = props.contractId
    const data = await SettlementRecordApi.getSettlementRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'create') {
    // 新增时传递合同信息
    const presetData = {
      contractId: props.contractId,
      customerId: contract.value?.customerId
    }
    formRef.value.open(type, id, presetData)
  } else {
    formRef.value.open(type, id)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SettlementRecordApi.deleteSettlementRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const params = {
      ...queryParams,
      contractId: props.contractId
    }
    const data = await SettlementRecordApi.exportSettlementRecord(params)
    download.excel(data, '结算单.xlsx')
    message.success('导出成功')
  } catch (error) {
    // 如果是用户取消导出确认，静默处理，不显示错误信息
    if (error === 'cancel' || error?.message === 'cancel' || error?.type === 'cancel') {
      console.log('用户取消导出操作')
      return
    }

    // 其他错误才显示错误提示
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

/** 监听合同ID变化 */
watch(
  () => props.contractId,
  async (newVal) => {
    if (newVal) {
      await getContractInfo() // 获取合同信息
      queryParams.pageNo = 1
      getList()
    }
  },
  { immediate: true }
)
</script>
