<template>
  <div>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="款号" prop="styleNo">
        <el-input
          v-model="queryParams.styleNo"
          placeholder="请输入款号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="颜色" prop="color">
        <el-input
          v-model="queryParams.color"
          placeholder="请输入颜色"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['crm:yard-list:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增款号
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="款号" align="center" prop="styleNo" />
      <el-table-column label="款式名称" align="center" prop="styleName" />
      <el-table-column label="颜色" align="center" prop="color" />
      <el-table-column label="规格" align="center" prop="specification" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="总米数" align="center" prop="totalQuantity" />
      <el-table-column label="总包数" align="center" prop="totalPackages" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openPackageList(scope.row)"
            v-hasPermi="['crm:yard-list:query']"
          >
            包号管理
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['crm:yard-list:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['crm:yard-list:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单弹窗：添加/修改款号 -->
    <!-- TODO: 需要创建 YardStyleForm 组件 -->

    <!-- 包号管理弹窗 -->
    <!-- TODO: 需要创建 YardPackageDialog 组件 -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useI18n } from '@/hooks/web/useI18n'

// TODO: 需要创建 YardStyle 相关的 API
// import { YardStyleApi, YardStyleVO } from '@/api/crm/yardlist/style'

// 临时类型定义
interface YardStyleVO {
  id?: number
  yardListId?: number
  styleNo?: string
  styleName?: string
  color?: string
  specification?: string
  unitPrice?: number
  totalQuantity?: number
}

/** CRM码单款号列表 */
defineOptions({ name: 'YardStyleList' })

const props = defineProps<{
  yardListId: number
}>()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<YardStyleVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  yardListId: props.yardListId,
  styleNo: undefined,
  color: undefined
})
const queryFormRef = ref<any>() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // TODO: 需要创建 YardStyleApi 后启用
    // const data = await YardStyleApi.getYardStylePage(queryParams)
    // list.value = data.list
    // total.value = data.total

    // 临时模拟数据
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
// TODO: 需要创建 YardStyleForm 组件后启用
// const formRef = ref<any>()
const openForm = (type: string, id?: number) => {
  // TODO: 需要创建 YardStyleForm 组件后启用
  console.log('openForm called:', type, id, props.yardListId)
  // formRef.value.open(type, id, props.yardListId)
}

/** 包号管理 */
// TODO: 需要创建 YardPackageDialog 组件后启用
// const packageDialogRef = ref<any>()
const openPackageList = (style: YardStyleVO) => {
  // TODO: 需要创建 YardPackageDialog 组件后启用
  console.log('openPackageList called:', style)
  // packageDialogRef.value.open(style)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // TODO: 需要创建 YardStyleApi 后启用
    // await YardStyleApi.deleteYardStyle(id)
    console.log('删除款号:', id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 监听码单ID变化 */
watch(() => props.yardListId, (newVal) => {
  if (newVal) {
    queryParams.yardListId = newVal
    getList()
  }
}, { immediate: true })
</script>
