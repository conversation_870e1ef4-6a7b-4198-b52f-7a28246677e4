<template>
  <ContentWrap v-loading="loading">
    <!-- 页面标题和操作按钮 -->
    <div class="mb-4" style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        <h1>码单详情</h1>
      </div>
      <div>
        <el-space>
          <el-button
            type="success"
            plain
            @click="handleExport"
            :loading="exportLoading"
            v-hasPermi="['crm:yard-list:export']"
          >
            <Icon icon="ep:download" class="mr-5px" /> 导出码单
          </el-button>
          <el-button
            type="primary"
            @click="handleEdit"
            v-hasPermi="['crm:yard-list:update']"
          >
            修改
          </el-button>
        </el-space>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" style="color: red; margin-bottom: 16px;">
      错误: {{ error }}
    </div>

    <!-- 码单基本信息 -->
    <el-card class="mb-4">
      <template #header>
        <span>码单基本信息</span>
      </template>

      <el-descriptions :column="3" border>
        <el-descriptions-item label="码单ID">
          {{ yardList.id || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="合同编号">
          <el-link
            v-if="contractNo && yardList.contractId"
            type="primary"
            :underline="false"
            @click="openContractDetail"
          >
            {{ contractNo }}
          </el-link>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">
          {{ customerName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="货品名称">
          {{ yardList.productName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总匹数">
          {{ yardList.totalPieces || 0 }} 匹
        </el-descriptions-item>
        <el-descriptions-item label="总数量">
          {{ Math.floor(yardList.totalMeters || 0) }} {{ getUnitText() }}
        </el-descriptions-item>
        <el-descriptions-item label="发货日期">
          {{ formatDate(yardList.deliveryDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="签收人">
          {{ yardList.recipient || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="负责人">
          {{ ownerUserName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">
          {{ yardList.remark || '无备注' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 款号包号详情 -->
    <el-card>
      <template #header>
        <span>码单款号包号详情 (共 {{ yardListDetails.length }} 条记录)</span>
      </template>

      <div v-if="yardListDetails && yardListDetails.length > 0">
        <el-table :data="yardListDetails" border stripe>
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="styleNo" label="款号" align="center">
            <template #default="{ row }">
              {{ row.styleNo || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="packageNo" label="包号" align="center">
            <template #default="{ row }">
              {{ row.packageNo || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" align="center">
            <template #default="{ row }">
              {{ row.quantity || 0 }} {{ getUnitText() }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 统计信息 -->
        <div style="margin-top: 16px; padding: 16px; background-color: #f5f5f5; border-radius: 4px;">
          <el-row :gutter="20">
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #409eff;">{{ yardListDetails.length }}</div>
                <div style="color: #909399;">总记录数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #67c23a;">{{ yardList.totalPieces || 0 }}</div>
                <div style="color: #909399;">总匹数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #e6a23c;">{{ totalQuantity }}</div>
                <div style="color: #909399;">总数量</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div v-else style="text-align: center; padding: 40px; color: #909399;">
        暂无款号包号详情数据
      </div>
    </el-card>
  </ContentWrap>

  <!-- 编辑表单弹窗 -->
  <YardListForm ref="formRef" @success="handleEditSuccess" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { YardListApi, YardListVO } from '@/api/crm/yardlist/index'
import { getContract } from '@/api/crm/contract'
import { getCustomer } from '@/api/crm/customer'
import { getUser } from '@/api/system/user'
import YardListForm from '../YardListForm.vue'
import download from '@/utils/download'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'

// 定义码单详情数据类型
interface YardListDetailItem {
  id?: number
  yardListId?: number
  styleNo?: string
  packageNo?: number
  quantity?: number
}

defineOptions({ name: 'CrmYardListDetail' })

const message = useMessage() // 消息弹窗
const route = useRoute()
const router = useRouter()
const yardListId = parseInt(route.params.id as string)

const loading = ref(true)
const exportLoading = ref(false) // 导出加载状态
const yardList = ref<YardListVO>({} as YardListVO)
const yardListDetails = ref<YardListDetailItem[]>([])
const contractNo = ref('') // 合同编号
const customerName = ref('') // 客户名称
const ownerUserName = ref('') // 负责人名称
const error = ref('')
const formRef = ref<any>() // 编辑表单引用

/** 获取码单数据 */
const getYardListData = async () => {
  loading.value = true
  error.value = ''
  try {
    console.log('正在获取码单数据，ID:', yardListId)
    // 获取主表数据
    yardList.value = await YardListApi.getYardList(yardListId)
    console.log('获取到的码单数据:', yardList.value)

    // 获取子表数据
    yardListDetails.value = await YardListApi.getYardListDetailListByYardListId(yardListId)
    console.log('获取到的码单详情数据:', yardListDetails.value)

    // 先获取负责人信息，判断是否已删除
    let isOwnerDeleted = false
    if (yardList.value.ownerUserId) {
      try {
        const user = await getUser(yardList.value.ownerUserId)
        ownerUserName.value = user.nickname || user.username || ''
        console.log('获取到的负责人名称:', ownerUserName.value)
      } catch (userError) {
        console.error('获取负责人名称失败:', userError)
        ownerUserName.value = `用户已删除(ID:${yardList.value.ownerUserId})`
        isOwnerDeleted = true
      }
    }

    // 如果负责人已删除，跳过合同和客户信息获取，避免权限问题
    if (!isOwnerDeleted) {
      // 获取合同编号
      if (yardList.value.contractId) {
        try {
          const contract = await getContract(yardList.value.contractId)
          contractNo.value = contract.no || ''
          console.log('获取到的合同编号:', contractNo.value)
        } catch (contractError) {
          console.error('获取合同编号失败:', contractError)
          contractNo.value = ''
        }
      }

      // 获取客户名称
      if (yardList.value.customerId) {
        try {
          const customer = await getCustomer(yardList.value.customerId)
          customerName.value = customer.name || ''
          console.log('获取到的客户名称:', customerName.value)
        } catch (customerError) {
          console.error('获取客户名称失败:', customerError)
          customerName.value = ''
        }
      }
    } else {
      // 负责人已删除，设置默认提示信息
      if (yardList.value.contractId) {
        contractNo.value = '负责人已删除'
      }
      if (yardList.value.customerId) {
        customerName.value = '负责人已删除'
      }
    }
  } catch (err: any) {
    console.error('获取码单数据失败:', err)
    error.value = '获取码单数据失败: ' + (err?.message || err)
  } finally {
    loading.value = false
  }
}

/** 计算总数量 */
const totalQuantity = computed(() => {
  if (!yardListDetails.value || yardListDetails.value.length === 0) {
    return '0'
  }
  const total = yardListDetails.value.reduce((sum, item) => {
    return sum + (Number(item.quantity) || 0)
  }, 0)
  return Math.floor(total).toString() // 转为整数
})

/** 获取单位文本 */
const getUnitText = () => {
  if (!yardList.value.unit && yardList.value.unit !== 0) {
    return '单位'
  }

  const unitOptions = getIntDictOptions(DICT_TYPE.CRM_PRODUCT_UNIT)

  // 尝试数字匹配和字符串匹配
  const unitOption = unitOptions.find(option => {
    const optionValue = option.value
    const unitValue = yardList.value.unit

    // 直接比较
    if (optionValue === unitValue) return true

    // 字符串比较
    if (String(optionValue) === String(unitValue)) return true

    // 数字比较
    const optionNum = Number(optionValue)
    const unitNum = Number(unitValue)
    return optionNum === unitNum && !Number.isNaN(optionNum) && !Number.isNaN(unitNum)
  })

  return unitOption ? unitOption.label : '单位'
}

/** 格式化日期为中文格式 */
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  const d = new Date(date)
  return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`
}

/** 打开合同详情 */
const openContractDetail = () => {
  if (yardList.value.contractId) {
    router.push({
      name: 'CrmContractDetail',
      params: { id: yardList.value.contractId }
    })
  }
}

/** 处理修改操作 */
const handleEdit = () => {
  formRef.value.open('update', yardListId)
}

/** 编辑成功后的处理 */
const handleEditSuccess = () => {
  message.success('码单修改成功')
  // 重新加载数据
  getYardListData()
}

/** 处理导出操作 */
const handleExport = async () => {
  try {
    // 导出确认
    await message.exportConfirm()

    // 设置导出加载状态
    exportLoading.value = true

    // 使用后端模板导出
    const data = await YardListApi.exportYardList(yardList.value.id)
    download.excel(data, `码单_${yardList.value.id}_${new Date().getTime()}.xlsx`)

    message.success('导出成功')
  } catch (error: any) {
    // 如果是用户取消导出确认，静默处理，不显示错误信息
    if (error === 'cancel' || error?.message === 'cancel' || error?.type === 'cancel') {
      console.log('用户取消导出操作')
      return
    }

    // 其他错误才显示错误提示
    console.error('导出失败:', error)
    message.error('导出失败: ' + (error?.message || error))
  } finally {
    // 重置导出加载状态
    exportLoading.value = false
  }
}

/** 初始化 */
onMounted(() => {
  console.log('详情页面初始化，码单ID:', yardListId)
  getYardListData()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
