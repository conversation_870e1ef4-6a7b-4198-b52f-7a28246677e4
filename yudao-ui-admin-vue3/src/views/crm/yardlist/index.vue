<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="发货日期" prop="deliveryDate">
        <el-date-picker
          v-model="queryParams.deliveryDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="searchCustomerId">
        <el-select
          v-model="queryParams.searchCustomerId"
          placeholder="请选择客户"
          filterable
          clearable
          class="!w-240px"
          @change="handleCustomerChange"
        >
          <el-option
            v-for="item in customerList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同名称" prop="contractId">
        <el-select
          v-model="queryParams.contractId"
          placeholder="请选择审核通过的合同"
          filterable
          clearable
          :disabled="!queryParams.searchCustomerId"
          class="!w-240px"
        >
          <el-option
            v-for="item in contractList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="item.auditStatus !== CRM_AUDIT_STATUS.APPROVE"
          >
            <span>{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              <dict-tag :type="DICT_TYPE.CRM_AUDIT_STATUS" :value="item.auditStatus" />
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['crm:yard-list:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <!-- 场景类型选择器 -->


    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="合同编号" align="center" width="200">
        <template #default="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="openContractDetail(scope.row.contractId)"
            v-if="scope.row.contractNo"
          >
            {{ scope.row.contractNo }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" width="200">
        <template #default="scope">
          {{ scope.row.customerName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" width="120">
        <template #default="scope">
          {{ scope.row.ownerUserName || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="发货日期" align="center" width="130">
        <template #default="scope">
          {{ formatDate(scope.row.deliveryDate) }}
        </template>
      </el-table-column>
      <el-table-column label="货品名称" align="center" prop="productName" min-width="110" />
      <el-table-column label="总匹数" align="center" prop="totalPieces" />
      <el-table-column label="总数量" align="center" prop="totalMeters" />
      <el-table-column label="单位" align="center" prop="unit" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CRM_PRODUCT_UNIT" :value="scope.row.unit" />
        </template>
      </el-table-column>
      <el-table-column label="签收人" align="center" prop="recipient" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="160">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail(scope.row.id)"
            v-hasPermi="['crm:yard-list:query']"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['crm:yard-list:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['crm:yard-list:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <YardListForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { dateFormatter } from '@/utils/formatTime'
import { YardListApi, YardListVO } from '@/api/crm/yardlist'
import { getContract, getContractPageByCustomer } from '@/api/crm/contract'
import { getCustomer, getCustomerSimpleList } from '@/api/crm/customer'
import { getUser } from '@/api/system/user'
import { DICT_TYPE } from '@/utils/dict'
import YardListForm from './YardListForm.vue'

/** CRM码单主 列表 */
defineOptions({ name: 'YardList' })

// 合同审核状态枚举
const CRM_AUDIT_STATUS = {
  DRAFT: 0,      // 未提交
  PROCESS: 10,   // 审批中
  APPROVE: 20,   // 审核通过
  REJECT: 30,    // 审核不通过
  CANCEL: 40     // 已取消
}



const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<YardListVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10, // 修改为正常的分页大小
  deliveryDate: [],
  contractNo: undefined,
  searchCustomerId: undefined,
  contractId: undefined,
  sceneType: null // 显示权限范围内的所有码单（我负责的、我参与的、下属负责的）
})

// 客户和合同列表
const customerList = ref<any[]>([])
const contractList = ref<any[]>([])
const queryFormRef = ref<any>() // 搜索的表单


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 构建查询参数，只传递后端需要的字段
    const searchParams = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      deliveryDate: queryParams.deliveryDate,
      contractNo: queryParams.contractNo,
      contractId: queryParams.contractId,
      customerId: queryParams.searchCustomerId, // 将searchCustomerId作为customerId传递给后端
      sceneType: queryParams.sceneType
    }
    console.log('码单查询参数:', searchParams)
    console.log('是否选择了客户:', !!searchParams.customerId, '客户ID:', searchParams.customerId)
    console.log('是否选择了合同:', !!searchParams.contractId, '合同ID:', searchParams.contractId)
    console.log('原始查询参数:', {
      searchCustomerId: queryParams.searchCustomerId,
      contractId: queryParams.contractId
    })
    const data = await YardListApi.getYardListPage(searchParams)
    list.value = data.list
    total.value = data.total

    // 获取合同编号和客户名称，在数据完全准备好之前保持loading状态
    await enrichContractData()
  } finally {
    loading.value = false
  }
}

/** 丰富合同数据 */
const enrichContractData = async () => {
  // 收集所有需要查询的用户ID
  const userIds = [...new Set(list.value.map(item => item.ownerUserId).filter(Boolean))]

  // 先获取用户信息，判断哪些用户已删除
  const userMap = await Promise.all(userIds.map(async id => {
    try {
      const user = await getUser(id)
      return { id, data: user }
    } catch (error: any) {
      console.error(`获取用户${id}失败:`, error)
      return { id, data: null }
    }
  })).then(results => new Map(results.map(r => [r.id, r.data])))

  // 根据用户状态，收集需要查询的合同和客户ID（只查询有效用户的）
  const validContractIds: number[] = []
  const validCustomerIds: number[] = []

  for (const item of list.value) {
    const user = userMap.get(item.ownerUserId)
    if (user) { // 只有用户有效时才获取合同和客户信息
      if (item.contractId) validContractIds.push(item.contractId as number)
      if (item.customerId) validCustomerIds.push(item.customerId as number)
    }
  }

  // 去重
  const contractIds = [...new Set(validContractIds)]
  const customerIds = [...new Set(validCustomerIds)]

  // 并行获取合同和客户数据（只获取有效用户的）
  const [contractMap, customerMap] = await Promise.all([
    // 获取合同信息
    Promise.all(contractIds.map(async id => {
      try {
        const contract = await getContract(id)
        return { id, data: contract }
      } catch (error: any) {
        console.error(`获取合同${id}失败:`, error)
        return { id, data: null }
      }
    })).then(results => new Map(results.map(r => [r.id, r.data]))),

    // 获取客户信息
    Promise.all(customerIds.map(async id => {
      try {
        const customer = await getCustomer(id)
        return { id, data: customer }
      } catch (error: any) {
        console.error(`获取客户${id}失败:`, error)
        return { id, data: null }
      }
    })).then(results => new Map(results.map(r => [r.id, r.data])))
  ])

  // 填充数据
  for (const item of list.value) {
    // 先检查负责人是否已删除
    let isOwnerDeleted = false
    if (item.ownerUserId) {
      const user = userMap.get(item.ownerUserId)
      if (user) {
        ;(item as any).ownerUserName = user.nickname || user.username || ''
      } else {
        // 用户已被删除或获取失败，显示提示信息
        ;(item as any).ownerUserName = `用户已删除(ID:${item.ownerUserId})`
        isOwnerDeleted = true
      }
    }

    // 如果负责人已删除，跳过合同和客户信息获取，避免权限问题
    if (!isOwnerDeleted) {
      // 设置合同编号
      if (item.contractId) {
        const contract = contractMap.get(item.contractId)
        ;(item as any).contractNo = contract?.no || ''
      }

      // 设置客户名称
      if (item.customerId) {
        const customer = customerMap.get(item.customerId)
        ;(item as any).customerName = customer?.name || ''
      }
    } else {
      // 负责人已删除，设置默认提示信息
      if (item.contractId) {
        ;(item as any).contractNo = '负责人已删除'
      }
      if (item.customerId) {
        ;(item as any).customerName = '负责人已删除'
      }
    }
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 清空客户和合同选择
  contractList.value = []
  queryParams.searchCustomerId = undefined
  queryParams.contractId = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref<any>()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await YardListApi.deleteYardList(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 打开合同详情 */
const openContractDetail = (contractId: number) => {
  if (contractId) {
    router.push({
      name: 'CrmContractDetail',
      params: { id: contractId }
    })
  }
}

/** 格式化日期为中文格式 */
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  const d = new Date(date)
  return `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日`
}

/** 获取客户列表 */
const getCustomerList = async () => {
  try {
    const data = await getCustomerSimpleList()
    customerList.value = data || []
  } catch (error: any) {
    console.error('获取客户列表失败:', error)
  }
}

/** 客户变化处理 */
const handleCustomerChange = async (customerId: number) => {
  queryParams.contractId = undefined
  contractList.value = []

  // 重要：更新查询参数中的客户ID
  queryParams.searchCustomerId = customerId

  if (customerId) {
    try {
      const data = await getContractPageByCustomer({
        pageNo: 1,
        pageSize: 50, // 减少页面大小，避免超过限制
        customerId: customerId,
        sceneType: null // 获取权限范围内的所有合同
      })
      contractList.value = data.list || []
      console.log('客户变更 - 客户ID:', customerId, '合同数量:', contractList.value.length)
    } catch (error: any) {
      console.error('获取合同列表失败:', error)
    }
  } else {
    console.log('客户变更 - 清空客户选择')
  }
}

/** 详情操作 */
const openDetail = (id: number) => {
  router.push({ name: 'CrmYardListDetail', params: { id } })
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCustomerList()
})
</script>
