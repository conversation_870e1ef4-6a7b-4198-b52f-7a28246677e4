<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border stripe :row-class-name="getRowClassName">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="款号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.styleNo`" :rules="formRules.styleNo" class="mb-0px!">
            <el-input
              v-model="row.styleNo"
              placeholder="请输入款号"
              @change="() => checkDuplicate($index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="包号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packageNo`" :rules="formRules.packageNo" class="mb-0px!">
            <el-input-number
              v-model="row.packageNo"
              placeholder="请输入包号"
              :min="1"
              :precision="0"
              :controls="true"
              controls-position="right"
              class="!w-1/1"
              @change="() => checkDuplicate($index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量（米数）" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input-number
              v-model="row.quantity"
              placeholder="请输入数量"
              :min="1"
              :precision="0"
              :step="1"
              :controls="true"
              controls-position="right"
              class="!w-1/1"
              @change="handleQuantityChange"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="80">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" type="danger" link size="small">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" type="primary" plain round>
      <el-icon><Plus /></el-icon>
      添加码单款号包号
    </el-button>
  </el-row>
</template>
<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { YardListApi } from '@/api/crm/yardlist'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义码单详情数据类型
interface YardListDetailItem {
  id?: number
  yardListId?: number
  styleNo?: string
  packageNo?: number
  quantity?: number
}

const props = defineProps<{
  yardListId: number | undefined // 码单主表ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<YardListDetailItem[]>([])
const formRules = reactive({
  yardListId: [{ required: true, message: '码单主表ID不能为空', trigger: 'blur' }],
  styleNo: [{ required: true, message: '款号不能为空', trigger: 'blur' }],
  packageNo: [{ required: true, message: '包号不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
})
const formRef = ref<any>() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.yardListId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await YardListApi.getYardListDetailListByYardListId(val)
      // 如果加载的数据为空，自动添加第一行
      if (formData.value.length === 0) {
        handleAdd()
      }
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  // 计算下一个包号
  const nextPackageNo = getNextPackageNo()

  // 获取上一个款号（如果存在）
  const lastStyleNo = formData.value.length > 0 ? formData.value[formData.value.length - 1].styleNo : undefined

  const row: YardListDetailItem = {
    id: undefined,
    yardListId: undefined,
    styleNo: lastStyleNo, // 自动复制上一个款号
    packageNo: nextPackageNo,
    quantity: 1,
  }
  row.yardListId = props.yardListId
  formData.value.push(row)
  // 通知父组件更新总计
  emitTotalChange()
}

/** 获取下一个包号 */
const getNextPackageNo = () => {
  if (formData.value.length === 0) {
    return 1 // 第一个包号从1开始
  }

  // 基于上一行的包号来递增
  const lastRow = formData.value[formData.value.length - 1]
  const lastPackageNo = Number(lastRow.packageNo) || 0
  return lastPackageNo + 1
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
  // 通知父组件更新总计
  emitTotalChange()
}

/** 数量变化时的处理 */
const handleQuantityChange = () => {
  // 通知父组件更新总计
  emitTotalChange()
}

/** 获取行样式类名 */
const getRowClassName = ({ row }: { row: YardListDetailItem }) => {
  // 检查当前行是否有重复
  if (row.styleNo && row.packageNo) {
    const duplicateCount = formData.value.filter(item =>
      item.styleNo === row.styleNo && item.packageNo === row.packageNo
    ).length

    if (duplicateCount > 1) {
      return 'duplicate-row'
    }
  }
  return ''
}

/** 检查重复的款号包号组合 */
const checkDuplicate = (currentIndex: number) => {
  const currentRow = formData.value[currentIndex]
  if (!currentRow.styleNo || !currentRow.packageNo) {
    return // 如果款号或包号为空，不检查
  }

  // 查找是否有相同的款号+包号组合
  const duplicateIndex = formData.value.findIndex((item, index) => {
    return index !== currentIndex &&
           item.styleNo === currentRow.styleNo &&
           item.packageNo === currentRow.packageNo
  })

  if (duplicateIndex !== -1) {
    ElMessage.warning(`款号"${currentRow.styleNo}"的包号"${currentRow.packageNo}"已存在，请修改！`)
    // 可以选择清空当前输入或者高亮显示重复项
    // currentRow.packageNo = undefined // 清空包号
  }
}

/** 计算总计并通知父组件 */
const emit = defineEmits<{
  totalChange: [data: { totalPieces: number; totalMeters: number }]
}>()
const emitTotalChange = () => {
  const totalPieces = formData.value.length // 总匹数 = 行数
  const totalMeters = formData.value.reduce((sum, item) => {
    return sum + (Number(item.quantity) || 0)
  }, 0) // 总米数 = 所有数量之和

  emit('totalChange', { totalPieces, totalMeters })
}

/** 表单校验 */
const validate = async () => {
  // 1. 先进行基础表单验证
  await formRef.value.validate()

  // 2. 进行重复检查
  const duplicates = findAllDuplicates()
  if (duplicates.length > 0) {
    const duplicateInfo = duplicates.map(dup => `款号"${dup.styleNo}"的包号"${dup.packageNo}"`).join('、')
    throw new Error(`存在重复的款号包号组合：${duplicateInfo}，请修改后再提交！`)
  }

  return true
}

/** 查找所有重复的款号包号组合 */
const findAllDuplicates = (): Array<{ styleNo: string; packageNo: number }> => {
  const seen = new Set<string>()
  const duplicates: Array<{ styleNo: string; packageNo: number }> = []

  formData.value.forEach((item) => {
    if (item.styleNo && item.packageNo) {
      const key = `${item.styleNo}-${item.packageNo}`
      if (seen.has(key)) {
        // 找到重复项
        if (!duplicates.some(dup => dup.styleNo === item.styleNo && dup.packageNo === item.packageNo)) {
          duplicates.push({ styleNo: item.styleNo, packageNo: item.packageNo })
        }
      } else {
        seen.add(key)
      }
    }
  })

  return duplicates
}

/** 表单值 */
const getData = (): YardListDetailItem[] => {
  return formData.value
}

defineExpose({ validate, getData })
</script>

<style scoped>
/* 重复行的样式 - 用于高亮显示重复的款号包号组合 */
:deep(.duplicate-row td) {
  background-color: #fef0f0 !important;
}

:deep(.duplicate-row:hover td) {
  background-color: #fde2e2 !important;
}
</style>
