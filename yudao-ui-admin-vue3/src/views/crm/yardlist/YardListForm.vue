<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <!-- 第一行：发货日期和负责人 -->
        <el-col :span="12">
          <el-form-item label="发货日期" prop="deliveryDate">
            <el-date-picker
              v-model="formData.deliveryDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择发货日期"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="ownerUserId">
            <el-select
              v-model="formData.ownerUserId"
              placeholder="请选择负责人"
              filterable
              :disabled="formType === 'update'"
              class="!w-1/1"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第二行：客户名称和合同名称 -->
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerId">
            <el-select
              v-model="formData.customerId"
              placeholder="请选择客户"
              filterable
              :disabled="formType === 'update'"
              class="!w-1/1"
              @change="handleCustomerChange"
            >
              <el-option
                v-for="item in customerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同名称" prop="contractId">
            <el-select
              v-model="formData.contractId"
              placeholder="请先选择客户"
              filterable
              :disabled="formType === 'update' || !formData.customerId"
              @change="handleContractChange"
              class="!w-1/1"
            >
              <el-option
                v-for="item in contractList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                :disabled="item.auditStatus !== CRM_AUDIT_STATUS.APPROVE"
              >
                <span>{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <dict-tag :type="DICT_TYPE.CRM_AUDIT_STATUS" :value="item.auditStatus" />
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第三行：货品名称和合同编号 -->
        <el-col :span="12">
          <el-form-item label="货品名称" prop="productName">
            <el-input v-model="formData.productName" placeholder="请输入货品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号">
            <el-input
              :value="selectedContract?.no || ''"
              readonly
              placeholder="选择合同后自动显示编号"
              class="!w-1/1"
              style="background-color: #f5f7fa;"
            >
              <template #prepend>
                <el-icon><Document /></el-icon>
              </template>
              <template #append v-if="selectedContract?.no">
                <el-button
                  @click="copyContractNo"
                  :icon="CopyDocument"
                  size="small"
                  text
                  title="复制合同编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第四行：签收人和单位 -->
        <el-col :span="12">
          <el-form-item label="签收人" prop="recipient">
            <el-input v-model="formData.recipient" placeholder="请输入签收人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-select v-model="formData.unit" placeholder="请选择单位" class="!w-1/1">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CRM_PRODUCT_UNIT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第五行：总匹数和总数量 -->
        <el-col :span="12">
          <el-form-item label="总匹数" prop="totalPieces">
            <el-input-number
              v-model="formData.totalPieces"
              placeholder="自动计算总匹数"
              :min="0"
              :precision="0"
              disabled
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总数量" prop="totalMeters">
            <el-input-number
              v-model="formData.totalMeters"
              placeholder="自动计算总数量"
              :min="0"
              :precision="3"
              disabled
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <!-- 第六行：备注 -->
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入备注"
              :rows="3"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="码单款号包号详情" name="yardListDetail">
        <YardListDetailForm ref="yardListDetailFormRef" :yard-list-id="formData.id" @total-change="handleTotalChange" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { YardListApi, YardListVO } from '@/api/crm/yardlist'
import { getCustomerSimpleList, CustomerVO } from '@/api/crm/customer'
import { getContractPageByCustomer, ContractVO } from '@/api/crm/contract'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, CopyDocument } from '@element-plus/icons-vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import YardListDetailForm from './components/YardListDetailForm.vue'

/** CRM码单主 表单 */
defineOptions({ name: 'YardListForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  contractId: undefined,
  customerId: undefined,
  ownerUserId: undefined,
  deliveryDate: undefined,
  productName: undefined,
  totalPieces: 0,
  totalMeters: 0,
  recipient: undefined,
  unit: undefined,
  remark: undefined,
})
const formRules = reactive({
  contractId: [{ required: true, message: '合同不能为空', trigger: 'change' }],
  customerId: [{ required: true, message: '客户不能为空', trigger: 'change' }],
  ownerUserId: [{ required: true, message: '负责人不能为空', trigger: 'change' }],
  deliveryDate: [{ required: true, message: '发货日期不能为空', trigger: 'change' }],
  unit: [{ required: true, message: '单位不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

// 选项数据
const customerList = ref<CustomerVO[]>([])
const contractList = ref<ContractVO[]>([])
const userList = ref<UserVO[]>([])
const selectedContract = ref<ContractVO | null>(null)

// 合同审核状态枚举
const CRM_AUDIT_STATUS = {
  DRAFT: 0,      // 未提交
  PROCESS: 10,   // 审批中
  APPROVE: 20,   // 审核通过
  REJECT: 30,    // 审核不通过
  CANCEL: 40     // 已取消
}

/** 子表的表单 */
const subTabsName = ref('yardListDetail')
const yardListDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 默认新建时立即选中自己（不需要等待API调用）
  if (formType.value === 'create') {
    formData.value.ownerUserId = useUserStore().getUser.id
  }

  // 加载基础数据
  await loadBaseData()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await YardListApi.getYardList(id)

      // 处理发货日期格式，确保日期选择器能正确显示
      if (data.deliveryDate !== null && data.deliveryDate !== undefined && data.deliveryDate !== '') {
        let processedDate = null

        // 如果是数组格式 [年, 月, 日]
        if (Array.isArray(data.deliveryDate) && data.deliveryDate.length === 3) {
          const [year, month, day] = data.deliveryDate
          const monthStr = month.toString().padStart(2, '0')
          const dayStr = day.toString().padStart(2, '0')
          processedDate = `${year}-${monthStr}-${dayStr}`
        }
        // 如果是完整的日期时间格式，只取日期部分
        else if (typeof data.deliveryDate === 'string' && data.deliveryDate.includes(' ')) {
          processedDate = data.deliveryDate.split(' ')[0]
        }
        // 如果是Date对象，转换为YYYY-MM-DD格式
        else if (data.deliveryDate instanceof Date) {
          processedDate = data.deliveryDate.toISOString().split('T')[0]
        }
        // 如果是时间戳数字，转换为YYYY-MM-DD格式
        else if (typeof data.deliveryDate === 'number') {
          processedDate = new Date(data.deliveryDate).toISOString().split('T')[0]
        }
        // 如果是纯日期字符串格式 (YYYY-MM-DD)
        else if (typeof data.deliveryDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(data.deliveryDate)) {
          processedDate = data.deliveryDate
        }
        // 如果是其他格式的字符串，尝试解析
        else if (typeof data.deliveryDate === 'string') {
          try {
            const date = new Date(data.deliveryDate)
            if (!isNaN(date.getTime())) {
              processedDate = date.toISOString().split('T')[0]
            }
          } catch (e) {
            // 解析失败，保持为null
          }
        }

        // 验证最终日期格式是否正确 (YYYY-MM-DD)
        if (processedDate && /^\d{4}-\d{2}-\d{2}$/.test(processedDate)) {
          data.deliveryDate = processedDate
        } else {
          data.deliveryDate = undefined
        }
      } else {
        data.deliveryDate = undefined
      }

      formData.value = data
      // 如果有客户ID，加载对应的合同列表
      if (formData.value.customerId) {
        await loadContractList(formData.value.customerId)
        // 如果有合同ID，设置选中的合同
        if (formData.value.contractId) {
          const contract = contractList.value.find(item => item.id === formData.value.contractId)
          if (contract) {
            selectedContract.value = contract
          }
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 加载基础数据 */
const loadBaseData = async () => {
  try {
    // 并行加载客户列表和用户列表
    const [customerData, userData] = await Promise.all([
      getCustomerSimpleList(),
      getSimpleUserList()
    ])
    customerList.value = customerData
    userList.value = userData
  } catch (error) {
    console.error('加载基础数据失败:', error)
  }
}

/** 获取审核状态文本 */
const getAuditStatusText = (status: number) => {
  const statusMap = {
    [CRM_AUDIT_STATUS.DRAFT]: '未提交',
    [CRM_AUDIT_STATUS.PROCESS]: '审批中',
    [CRM_AUDIT_STATUS.APPROVE]: '审核通过',
    [CRM_AUDIT_STATUS.REJECT]: '审核不通过',
    [CRM_AUDIT_STATUS.CANCEL]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

/** 客户变化时的处理 */
const handleCustomerChange = async (customerId: number) => {
  // 清空合同选择和合同编号显示
  formData.value.contractId = undefined
  contractList.value = []
  selectedContract.value = null

  if (customerId) {
    await loadContractList(customerId)
  }
}

/** 加载合同列表 */
const loadContractList = async (customerId: number) => {
  try {
    const contractData = await getContractPageByCustomer({
      customerId: customerId,
      pageNo: 1,
      pageSize: 100
    })
    contractList.value = contractData.list || []
  } catch (error) {
    console.error('加载合同列表失败:', error)
    contractList.value = []
  }
}

/** 处理子表总计变化 */
const handleTotalChange = ({ totalPieces, totalMeters }) => {
  formData.value.totalPieces = totalPieces
  formData.value.totalMeters = totalMeters
}

/** 复制合同编号 */
const copyContractNo = async () => {
  if (selectedContract.value?.no) {
    try {
      await navigator.clipboard.writeText(selectedContract.value.no)
      ElMessage.success('合同编号已复制到剪贴板')
    } catch (error) {
      // 如果现代API不可用，使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = selectedContract.value.no
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('合同编号已复制到剪贴板')
    }
  }
}

/** 合同变化时的处理 */
const handleContractChange = (contractId: number) => {
  if (contractId) {
    // 查找选中的合同
    const contract = contractList.value.find(item => item.id === contractId)
    if (contract) {
      // 检查审核状态 - 只有审核通过的合同才能选择
      if (contract.auditStatus !== CRM_AUDIT_STATUS.APPROVE) {
        // 如果不是审核通过状态，显示警告并清空选择
        ElMessageBox.alert(
          `该合同状态为"${getAuditStatusText(contract.auditStatus)}"，只有审核通过的合同才能选择。`,
          '合同状态提示',
          {
            confirmButtonText: '确定',
            type: 'warning'
          }
        )
        formData.value.contractId = undefined
        selectedContract.value = null
        return
      }
      // 设置选中的合同信息
      selectedContract.value = contract
    }
  } else {
    selectedContract.value = null
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await yardListDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'yardListDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as YardListVO
    // 拼接子表的数据
    data.yardListDetails = yardListDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await YardListApi.createYardList(data)
      message.success(t('common.createSuccess'))
    } else {
      await YardListApi.updateYardList(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    contractId: undefined,
    customerId: undefined,
    ownerUserId: undefined,
    deliveryDate: undefined,
    productName: undefined,
    totalPieces: 0,
    totalMeters: 0,
    recipient: undefined,
    unit: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
  // 清空选项数据
  contractList.value = []
  selectedContract.value = null
}
</script>
