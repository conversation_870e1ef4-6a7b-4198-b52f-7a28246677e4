<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="发货日期" prop="settlementDate">
        <el-date-picker
          v-model="queryParams.settlementDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="searchCustomerId">
        <el-select
          v-model="queryParams.searchCustomerId"
          placeholder="请选择客户"
          filterable
          clearable
          class="!w-240px"
          @change="handleCustomerChange"
        >
          <el-option
            v-for="item in customerList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合同名称" prop="contractId">
        <el-select
          v-model="queryParams.contractId"
          placeholder="请选择审核通过的合同"
          filterable
          clearable
          :disabled="!queryParams.searchCustomerId"
          class="!w-240px"
        >
          <el-option
            v-for="item in contractList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            :disabled="item.auditStatus !== CRM_AUDIT_STATUS.APPROVE"
            :class="{ 'contract-disabled': item.auditStatus !== CRM_AUDIT_STATUS.APPROVE }"
          >
            <span :style="{ color: item.auditStatus !== CRM_AUDIT_STATUS.APPROVE ? '#c0c4cc' : '' }">
              {{ item.name }}
            </span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              <dict-tag :type="DICT_TYPE.CRM_AUDIT_STATUS" :value="item.auditStatus" />
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['crm:settlement-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="发货日期" align="center" prop="settlementDate" width="130">
        <template #default="scope">
          {{ formatDate(scope.row.settlementDate) }}
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center" prop="contractNo" width="180">
        <template #default="scope">
          <el-link 
            v-if="scope.row.contractNo" 
            type="primary" 
            :underline="false"
            @click="openContractDetail(scope.row.contractId)"
          >
            {{ scope.row.contractNo }}
          </el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" width="200" />
      <el-table-column label="负责人" align="center" prop="ownerUserName" width="100" />
      <el-table-column label="品名/款号" align="center" prop="productNameCode" width="120" />
      <el-table-column label="成分" align="center" prop="composition" width="100" />
      <el-table-column label="规格" align="center" prop="specification" width="100" />
      <el-table-column label="克重" align="center" prop="weight" width="80" />
      <el-table-column label="幅宽" align="center" prop="width" width="80" />
      <el-table-column label="数量" align="center" prop="quantity" width="80" />
      <el-table-column label="单位" align="center" prop="unit" width="60">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CRM_PRODUCT_UNIT" :value="scope.row.unit" />
        </template>
      </el-table-column>
      <el-table-column label="单价（CNY/USD）" align="center" prop="unitPrice" width="120" />
      <el-table-column label="金额（CNY/USD）" align="center" prop="totalAmount" width="120" />
      <el-table-column label="备注" align="center" prop="remark" width="120" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['crm:settlement-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['crm:settlement-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SettlementRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { SettlementRecordApi, SettlementRecordVO } from '@/api/crm/settlementrecord'
import SettlementRecordForm from './SettlementRecordForm.vue'
import * as ContractApi from '@/api/crm/contract'
import * as CustomerApi from '@/api/crm/customer'

/** CRM结算单列表 */
defineOptions({ name: 'SettlementRecord' })

// 合同审核状态枚举
const CRM_AUDIT_STATUS = {
  DRAFT: 0,      // 未提交
  PROCESS: 10,   // 审批中
  APPROVE: 20,   // 审核通过
  REJECT: 30     // 审核不通过
}

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<SettlementRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const customerList = ref<CustomerApi.CustomerVO[]>([]) // 客户列表
const contractList = ref<ContractApi.ContractVO[]>([]) // 合同列表

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  settlementDate: [],
  contractNo: undefined, // 合同编号搜索
  contractId: undefined, // 合同ID搜索
  searchCustomerId: undefined, // 用于搜索的客户ID，不传给后端
  searchCustomerContracts: undefined, // 客户的所有合同ID列表，用于按客户搜索
})
const queryFormRef = ref() // 搜索的表单

/** 格式化日期为中文格式 */
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

/** 打开合同详情 */
const openContractDetail = (contractId: number) => {
  if (!contractId) return
  // 跳转到合同详情页面
  router.push({
    name: 'CrmContractDetail',
    params: { id: contractId }
  })
}

/** 处理客户变更 */
const handleCustomerChange = async (customerId: number) => {
  console.log('=== 搜索客户变更 ===')
  console.log('客户ID:', customerId)

  // 清空合同选择和客户合同关联
  queryParams.contractId = undefined
  queryParams.searchCustomerContracts = undefined
  contractList.value = []

  if (!customerId) {
    return
  }

  try {
    // 加载该客户的所有合同
    console.log('开始加载客户合同...')
    const data = await ContractApi.getContractSimpleList(customerId)
    contractList.value = data || []
    console.log('加载的合同列表:', contractList.value)
    console.log('合同数量:', contractList.value.length)

    // 设置客户的合同ID列表，但不自动搜索
    if (contractList.value.length > 0) {
      queryParams.searchCustomerContracts = contractList.value.map(contract => contract.id)
    }
  } catch (error) {
    console.error('获取客户合同失败:', error)
    message.error('获取客户合同失败')
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let searchParams

    // 如果是按客户搜索（有客户的合同ID列表）且没有选择具体合同
    if (queryParams.searchCustomerContracts && queryParams.searchCustomerContracts.length > 0 && !queryParams.contractId) {
      // 查询该客户所有合同的结算记录
      const allResults = []

      // 分批查询，每次最多查询10个合同，避免请求过多
      const batchSize = 10
      const contractBatches = []
      for (let i = 0; i < queryParams.searchCustomerContracts.length; i += batchSize) {
        contractBatches.push(queryParams.searchCustomerContracts.slice(i, i + batchSize))
      }

      for (const batch of contractBatches) {
        const batchPromises = batch.map(contractId => {
          const contractParams = {
            pageNo: 1,
            pageSize: 50, // 减少单次查询的数量
            settlementDate: queryParams.settlementDate,
            contractNo: queryParams.contractNo,
            contractId: contractId
          }
          return SettlementRecordApi.getSettlementRecordPage(contractParams)
        })

        const batchResults = await Promise.all(batchPromises)
        batchResults.forEach(result => {
          allResults.push(...result.list)
        })
      }

      // 手动分页
      const startIndex = (queryParams.pageNo - 1) * queryParams.pageSize
      const endIndex = startIndex + queryParams.pageSize
      list.value = allResults.slice(startIndex, endIndex)
      total.value = allResults.length
      loading.value = false
      return
    }

    // 正常查询（包括选择了具体合同的情况）
    searchParams = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      settlementDate: queryParams.settlementDate,
      contractNo: queryParams.contractNo,
      contractId: queryParams.contractId
    }

    console.log('查询参数:', searchParams)
    const data = await SettlementRecordApi.getSettlementRecordPage(searchParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 清空合同列表和客户合同关联
  contractList.value = []
  queryParams.searchCustomerContracts = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SettlementRecordApi.deleteSettlementRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}



/** 初始化 **/
onMounted(async () => {
  // 获取客户列表（预加载所有客户，支持本地过滤）
  customerList.value = await CustomerApi.getCustomerSimpleList()
  // 获取数据列表
  getList()
})
</script>

<style scoped>
.contract-disabled {
  opacity: 0.6;
}
</style>
