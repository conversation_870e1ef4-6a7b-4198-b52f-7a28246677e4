<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row>
        <!-- 第一行：发货日期和负责人 -->
        <el-col :span="12">
          <el-form-item label="发货日期" prop="settlementDate">
            <el-date-picker
              v-model="formData.settlementDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择发货日期"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="ownerUserId">
            <el-select
              v-model="formData.ownerUserId"
              placeholder="请选择负责人"
              filterable
              :disabled="formType === 'update'"
              class="!w-1/1"
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <!-- 第二行：客户名称和合同名称 -->
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerId">
            <el-select
              v-model="formData.customerId"
              placeholder="请选择客户"
              filterable
              :disabled="formType === 'update'"
              class="!w-1/1"
              @change="handleCustomerChange"
            >
              <el-option
                v-for="item in customerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同名称" prop="contractId">
            <el-select
              v-model="formData.contractId"
              placeholder="请先选择客户"
              filterable
              :disabled="formType === 'update' || !formData.customerId"
              @change="handleContractChange"
              class="!w-1/1"
            >
              <el-option
                v-for="item in contractList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                :disabled="item.auditStatus !== 20"
              >
                <span>{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <dict-tag :type="DICT_TYPE.CRM_AUDIT_STATUS" :value="item.auditStatus" />
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <!-- 第三行：品名/款号和合同编号 -->
        <el-col :span="12">
          <el-form-item label="品名/款号" prop="productNameCode">
            <el-input v-model="formData.productNameCode" placeholder="请输入品名/款号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号">
            <el-input v-model="selectedContractNo" disabled placeholder="选择合同后自动显示" />
          </el-form-item>
        </el-col>
        
        <!-- 第四行：成分和规格 -->
        <el-col :span="12">
          <el-form-item label="成分" prop="composition">
            <el-input v-model="formData.composition" placeholder="请输入成分" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格" prop="specification">
            <el-input v-model="formData.specification" placeholder="请输入规格" />
          </el-form-item>
        </el-col>
        
        <!-- 第五行：克重和幅宽 -->
        <el-col :span="12">
          <el-form-item label="克重" prop="weight">
            <el-input-number
              v-model="formData.weight"
              placeholder="请输入克重"
              :min="0"
              :precision="3"
              class="!w-1/1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="幅宽" prop="width">
            <el-input v-model="formData.width" placeholder="请输入幅宽，如：150cm" />
          </el-form-item>
        </el-col>
        
        <!-- 第六行：数量和单位 -->
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              placeholder="请输入数量"
              :min="0"
              :precision="3"
              class="!w-1/1"
              @change="calculateTotalAmount"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-select v-model="formData.unit" placeholder="请选择单位" class="!w-1/1">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CRM_PRODUCT_UNIT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <!-- 第七行：单价和金额 -->
        <el-col :span="12">
          <el-form-item label="单价 CNY/USD" prop="unitPrice">
            <el-input-number
              v-model="formData.unitPrice"
              placeholder="请输入单价"
              :min="0"
              :precision="2"
              class="!w-1/1"
              @change="calculateTotalAmount"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="totalAmount">
            <el-input-number
              v-model="formData.totalAmount"
              placeholder="自动计算"
              :min="0"
              :precision="2"
              class="!w-1/1"
              disabled
            />
          </el-form-item>
        </el-col>
        
        <!-- 第八行：备注（占满整行） -->
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input 
              v-model="formData.remark" 
              placeholder="请输入备注" 
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { SettlementRecordApi, SettlementRecordVO } from '@/api/crm/settlementrecord'
import * as ContractApi from '@/api/crm/contract'
import * as CustomerApi from '@/api/crm/customer'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

/** CRM结算单表单 */
defineOptions({ name: 'SettlementRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userStore = useUserStore() // 用户信息

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const customerList = ref<CustomerApi.CustomerVO[]>([]) // 客户列表
const contractList = ref<ContractApi.ContractVO[]>([]) // 合同列表
const userList = ref<UserVO[]>([]) // 用户列表
const selectedContractNo = ref('') // 选中的合同编号（仅用于显示）

// 合同审核状态枚举
const CRM_AUDIT_STATUS = {
  DRAFT: 0,      // 未提交
  PROCESS: 10,   // 审批中
  APPROVE: 20,   // 审核通过
  REJECT: 30,    // 审核不通过
  CANCEL: 40     // 已取消
}

const formData = ref({
  id: undefined,
  settlementDate: undefined,
  productNameCode: undefined,
  composition: undefined,
  specification: undefined,
  weight: undefined,
  width: undefined,
  quantity: undefined,
  unit: 1, // 默认单位
  unitPrice: undefined,
  totalAmount: undefined,
  remark: undefined,
  ownerUserId: undefined,
  customerId: undefined,
  contractId: undefined,
})

const formRules = reactive({
  settlementDate: [{ required: true, message: '发货日期不能为空', trigger: 'blur' }],
  ownerUserId: [{ required: true, message: '负责人不能为空', trigger: 'change' }],
  customerId: [{ required: true, message: '客户名称不能为空', trigger: 'change' }],
  contractId: [{ required: true, message: '合同名称不能为空', trigger: 'change' }],
  productNameCode: [{ required: true, message: '品名/款号不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '单价（元）不能为空', trigger: 'blur' }],
})

const formRef = ref() // 表单 Ref

/** 计算总金额 */
const calculateTotalAmount = () => {
  if (formData.value.quantity && formData.value.unitPrice) {
    formData.value.totalAmount = Number((formData.value.quantity * formData.value.unitPrice).toFixed(2))
  }
}

/** 获取审核状态文本 */
const getAuditStatusText = (status: number) => {
  const statusMap = {
    [CRM_AUDIT_STATUS.DRAFT]: '未提交',
    [CRM_AUDIT_STATUS.PROCESS]: '审批中',
    [CRM_AUDIT_STATUS.APPROVE]: '审核通过',
    [CRM_AUDIT_STATUS.REJECT]: '审核不通过',
    [CRM_AUDIT_STATUS.CANCEL]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

/** 处理客户变更 */
const handleCustomerChange = async (customerId: number) => {
  console.log('=== 客户变更 ===')
  console.log('客户ID:', customerId)
  // 清空合同相关数据
  formData.value.contractId = undefined
  selectedContractNo.value = ''
  contractList.value = []
  
  if (!customerId) {
    return
  }
  
  try {
    // 加载该客户的所有合同
    console.log('开始加载客户合同...')
    const data = await ContractApi.getContractSimpleList(customerId)
    contractList.value = data || []
    console.log('加载的合同列表:', contractList.value)
    console.log('合同数量:', contractList.value.length)
  } catch (error) {
    console.error('获取客户合同失败:', error)
    message.error('获取客户合同失败')
  }
}

/** 处理合同变更 */
const handleContractChange = async (contractId: number) => {
  console.log('=== 合同变更 ===')
  console.log('合同ID:', contractId)
  if (!contractId) {
    selectedContractNo.value = ''
    return
  }
  
  try {
    const contract = contractList.value.find(item => item.id === contractId)
    console.log('找到的合同对象:', contract)
    if (contract) {
      console.log('合同审核状态:', contract.auditStatus)
      console.log('状态文本:', getAuditStatusText(contract.auditStatus))
      console.log('合同编号(no字段):', contract.no)
      
      // 设置合同编号显示（仅用于前端显示，不提交给后端）
      if (contract.no && contract.no.toString().trim() !== '') {
        selectedContractNo.value = contract.no.toString()
        console.log('设置合同编号:', selectedContractNo.value)
      } else {
        selectedContractNo.value = '未设置合同编号'
        console.log('合同编号为空，显示提示信息')
      }
      
      // 检查合同状态是否为审核通过
      if (contract.auditStatus !== CRM_AUDIT_STATUS.APPROVE) {
        const statusText = getAuditStatusText(contract.auditStatus)
        message.warning(`该合同状态为"${statusText}"，无法创建结算单。只有审核通过的合同才能创建结算单。`)
        
        // 清空选择
        formData.value.contractId = undefined
        selectedContractNo.value = ''
        return
      }
      
      console.log('合同状态检查通过，可以创建结算单')
    } else {
      console.log('未找到合同对象')
      selectedContractNo.value = ''
    }
  } catch (error) {
    console.error('获取合同信息失败:', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, presetData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await SettlementRecordApi.getSettlementRecord(id)

      // 处理日期格式，确保日期选择器能正确显示
      if (data.settlementDate !== null && data.settlementDate !== undefined && data.settlementDate !== '') {
        let processedDate = null

        // 如果是数组格式 [年, 月, 日]
        if (Array.isArray(data.settlementDate) && data.settlementDate.length === 3) {
          const [year, month, day] = data.settlementDate
          const monthStr = month.toString().padStart(2, '0')
          const dayStr = day.toString().padStart(2, '0')
          processedDate = `${year}-${monthStr}-${dayStr}`
        }
        // 如果是完整的日期时间格式，只取日期部分
        else if (typeof data.settlementDate === 'string' && data.settlementDate.includes(' ')) {
          processedDate = data.settlementDate.split(' ')[0]
        }
        // 如果是Date对象，转换为YYYY-MM-DD格式
        else if (data.settlementDate instanceof Date) {
          processedDate = data.settlementDate.toISOString().split('T')[0]
        }
        // 如果是时间戳数字，转换为YYYY-MM-DD格式
        else if (typeof data.settlementDate === 'number') {
          processedDate = new Date(data.settlementDate).toISOString().split('T')[0]
        }
        // 如果是纯日期字符串格式 (YYYY-MM-DD)
        else if (typeof data.settlementDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(data.settlementDate)) {
          processedDate = data.settlementDate
        }
        // 如果是其他格式的字符串，尝试解析
        else if (typeof data.settlementDate === 'string') {
          try {
            const date = new Date(data.settlementDate)
            if (!isNaN(date.getTime())) {
              processedDate = date.toISOString().split('T')[0]
            }
          } catch (e) {
            // 解析失败，保持为null
          }
        }

        // 验证最终日期格式是否正确 (YYYY-MM-DD)
        if (processedDate && /^\d{4}-\d{2}-\d{2}$/.test(processedDate)) {
          data.settlementDate = processedDate
        } else {
          data.settlementDate = undefined
        }
      } else {
        data.settlementDate = undefined
      }

      formData.value = data
      
      // 如果有客户ID，需要加载该客户的合同列表
      if (data.customerId) {
        const contractData = await ContractApi.getContractSimpleList(data.customerId)
        contractList.value = contractData || []
        
        // 设置合同编号显示
        if (data.contractId) {
          const contract = contractList.value.find(item => item.id === data.contractId)
          if (contract) {
            selectedContractNo.value = contract.no || '未设置合同编号'
          }
        }
      }
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时设置默认负责人为当前用户
    formData.value.ownerUserId = userStore.getUser.id
    
    // 如果有预设数据（从合同详情页面进入）
    if (presetData) {
      console.log('=== 预设数据 ===', presetData)
      if (presetData.contractId) {
        formData.value.contractId = presetData.contractId
        formData.value.customerId = presetData.customerId
        
        // 加载客户的合同列表
        if (presetData.customerId) {
          try {
            const contractData = await ContractApi.getContractSimpleList(presetData.customerId)
            contractList.value = contractData || []
            
            // 设置合同编号显示
            const contract = contractList.value.find(item => item.id === presetData.contractId)
            if (contract) {
              selectedContractNo.value = contract.no || '未设置合同编号'
            }
          } catch (error) {
            console.error('预设数据加载合同失败:', error)
          }
        }
      }
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 提交前的数据检查
  console.log('=== 提交前数据检查 ===')
  console.log('结算日期:', formData.value.settlementDate)
  console.log('负责人ID:', formData.value.ownerUserId)
  console.log('客户ID:', formData.value.customerId)
  console.log('合同ID:', formData.value.contractId)
  console.log('显示的合同编号:', selectedContractNo.value)
  console.log('完整数据:', formData.value)
  
  // 检查必要字段
  if (!formData.value.contractId) {
    message.error('请选择合同名称')
    return
  }
  if (!formData.value.customerId) {
    message.error('请选择客户名称')
    return
  }
  if (!formData.value.ownerUserId) {
    message.error('请选择负责人')
    return
  }
  
  // 再次检查合同状态
  const contract = contractList.value.find(item => item.id === formData.value.contractId)
  if (contract && contract.auditStatus !== CRM_AUDIT_STATUS.APPROVE) {
    const statusText = getAuditStatusText(contract.auditStatus)
    message.error(`合同状态为"${statusText}"，无法创建结算单`)
    return
  }
  
  // 准备提交数据，只提交后端需要的字段
  const submitData = {
    id: formData.value.id,
    settlementDate: formData.value.settlementDate,
    productNameCode: formData.value.productNameCode,
    composition: formData.value.composition,
    specification: formData.value.specification,
    weight: formData.value.weight,
    width: formData.value.width,
    quantity: formData.value.quantity,
    unit: formData.value.unit,
    unitPrice: formData.value.unitPrice,
    totalAmount: formData.value.totalAmount,
    remark: formData.value.remark,
    // 关键字段：只提交ID
    contractId: Number(formData.value.contractId),
    customerId: Number(formData.value.customerId),
    ownerUserId: Number(formData.value.ownerUserId)
  }
  
  console.log('最终提交数据:', submitData)
  
  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await SettlementRecordApi.createSettlementRecord(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await SettlementRecordApi.updateSettlementRecord(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    console.error('错误详情:', error.response?.data)
    const errorMsg = error.response?.data?.msg || error.message || '请联系管理员'
    message.error('提交失败：' + errorMsg)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    settlementDate: undefined,
    productNameCode: undefined,
    composition: undefined,
    specification: undefined,
    weight: undefined,
    width: undefined,
    quantity: undefined,
    unit: 1, // 默认单位
    unitPrice: undefined,
    totalAmount: undefined,
    remark: undefined,
    ownerUserId: undefined,
    customerId: undefined,
    contractId: undefined,
  }
  selectedContractNo.value = '' // 清空合同编号显示
  contractList.value = [] // 清空合同列表
  formRef.value?.resetFields()
}

/** 初始化 */
onMounted(async () => {
  // 获取用户列表
  userList.value = await getSimpleUserList()
  // 获取客户列表（预加载所有客户，支持本地过滤）
  customerList.value = await CustomerApi.getCustomerSimpleList()
})
</script>

<style scoped>
:deep(.el-select-dropdown__item.is-disabled) {
  color: #c0c4cc;
  cursor: not-allowed;
}

:deep(.el-select-dropdown__item.is-disabled .dict-tag) {
  opacity: 0.6;
}
</style>
