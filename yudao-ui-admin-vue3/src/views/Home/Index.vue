<template>
  <div>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="70" class="mr-16px">
            <img src="@/assets/imgs/avatar.gif" alt="" />
          </el-avatar>
          <div>
            <div class="text-20px">
              {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
            </div>
            <div class="mt-10px text-14px text-gray-500">
              旭远纺织CRM管理系统，助力业务高效运营！
            </div>
          </div>
        </div>
      </el-skeleton>
    </el-card>
  </div>

  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>业务模块</span>
            <el-link
              type="primary"
              :underline="false"
              @click="router.push('/crm/backlog')"
            >
              {{ t('action.more') }}
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col
              v-for="(item, index) in projects"
              :key="`card-${index}`"
              :xl="8"
              :lg="8"
              :md="8"
              :sm="24"
              :xs="24"
            >
              <el-card
                shadow="hover"
                class="mr-5px mt-5px cursor-pointer"
                @click="handleProjectClick(item.message)"
              >
                <div class="flex items-center">
                  <Icon
                    :icon="item.icon"
                    :size="25"
                    class="mr-8px"
                    :style="{ color: item.color }"
                  />
                  <span class="text-16px">{{ item.name }}</span>
                </div>
                <div class="mt-12px text-12px text-gray-400">{{ t(item.message) }}</div>
                <div class="mt-12px flex justify-between items-center">
                  <span class="text-12px text-gray-400">{{ item.personal }}</span>
                  <!-- 统计数据标签 - 右下角与第三行平齐 -->
                  <div
                    class="flex items-center px-2 py-1 rounded-full text-white text-xs"
                    :style="{ backgroundColor: item.color }"
                  >
                    <span class="mr-1">{{ item.statLabel }}</span>
                    <span class="font-bold">{{ item.statValue }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>

      <el-card shadow="never" class="mt-8px">
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="20" justify="space-between">
            <el-col :xl="10" :lg="10" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <el-skeleton :loading="loading" animated>
                  <Echart :options="pieOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
            <el-col :xl="14" :lg="14" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <el-skeleton :loading="loading" animated>
                  <Echart :options="barOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
    </el-col>
    <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>{{ t('workplace.shortcutOperation') }}</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col v-for="item in shortcut" :key="`team-${item.name}`" :span="8" class="mb-8px">
              <div class="flex items-center">
                <Icon :icon="item.icon" class="mr-8px" :style="{ color: item.color }" />
                <el-link type="default" :underline="false" @click="handleShortcutClick(item.url)">
                  {{ item.name }}
                </el-link>
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>我负责的合同</span>
            <el-link type="primary" :underline="false" @click="router.push('/crm/contract')">
              {{ t('action.more') }}
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-for="(item, index) in myContracts" :key="`contract-${index}`">
            <div class="flex items-center">
              <el-avatar :size="35" class="mr-16px" style="background-color: #409EFF;">
                <Icon icon="ep:document" color="white" />
              </el-avatar>
              <div class="flex-1">
                <div class="text-14px">
                  <el-link
                    type="primary"
                    :underline="false"
                    @click="openContractDetail(item.id)"
                  >
                    {{ item.name }}
                  </el-link>
                </div>
                <div class="mt-4px text-12px text-gray-400">
                  合同编号：{{ item.no }} | 客户：{{ item.customerName }}
                </div>
                <div class="mt-4px text-12px text-gray-400">
                  创建时间：{{ formatTime(item.createTime, 'yyyy-MM-dd') }}
                </div>
              </div>
              <div class="text-right">
                <dict-tag :type="DICT_TYPE.CRM_AUDIT_STATUS" :value="item.auditStatus" />
              </div>
            </div>
            <el-divider v-if="index < myContracts.length - 1" />
          </div>
          <div v-if="myContracts.length === 0" class="text-center text-gray-400 py-20px">
            暂无负责的合同
          </div>
        </el-skeleton>
      </el-card>
    </el-col>
  </el-row>
</template>
<script lang="ts" setup>
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import { formatTime } from '@/utils'

import { useUserStore } from '@/store/modules/user'
// import { useWatermark } from '@/hooks/web/useWatermark'
import type { WorkplaceTotal, Project, Notice, Shortcut } from './types'
import { pieOptions, barOptions } from './echarts-data'
import { useRouter } from 'vue-router'
import { getContractPage } from '@/api/crm/contract'
import { getCustomerPage } from '@/api/crm/customer'
import { getProductPage } from '@/api/crm/product'
import { YardListApi } from '@/api/crm/yardlist'
import { SettlementRecordApi } from '@/api/crm/settlementrecord'
import { DICT_TYPE } from '@/utils/dict'

defineOptions({ name: 'Index' })

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
// const { setWatermark } = useWatermark()
const loading = ref(true)
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const pieOptionsData = reactive<EChartsOption>(pieOptions) as EChartsOption


// 获取业务模块数
let projects = reactive<Project[]>([])
const getProject = async () => {
  const data = [
    {
      name: '客户管理',
      icon: 'ep:user',
      message: '/crm/customer',
      personal: '客户信息管理与维护',
      color: '#409EFF',
      statLabel: '我的客户',
      statValue: '0'
    },
    {
      name: '合同管理',
      icon: 'ep:document',
      message: '/crm/contract',
      personal: '合同签订与跟踪管理',
      color: '#67C23A',
      statLabel: '我的待审批',
      statValue: '0'
    },
    {
      name: '码单管理',
      icon: 'ep:list',
      message: '/crm/yard-list',
      personal: '发货码单与包装管理',
      color: '#E6A23C',
      statLabel: '我的码单',
      statValue: '0'
    },
    {
      name: '结算管理',
      icon: 'ep:money',
      message: '/crm/settlement-record',
      personal: '结算单据与财务管理',
      color: '#F56C6C',
      statLabel: '我的结算',
      statValue: '0'
    },
    {
      name: '产品管理',
      icon: 'ep:goods',
      message: '/crm/product',
      personal: '产品信息与规格管理',
      color: '#909399',
      statLabel: '我的产品',
      statValue: '0'
    },
    {
      name: '客户分析',
      icon: 'ep:data-analysis',
      message: '/crm/statistics/customer',
      personal: '客户数据分析与报表',
      color: '#9C27B0',
      statLabel: '我的新增',
      statValue: '0'
    }
  ]
  projects = Object.assign(projects, data)
  // 获取实时统计数据
  await getProjectStats()
}

// 获取各模块的统计数据
const getProjectStats = async () => {
  try {
    // 1. 获取我负责的客户总数（sceneType=1表示我负责的）
    const customerStats = await getCustomerPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1
    })
    if (customerStats && customerStats.total !== undefined) {
      projects[0].statValue = customerStats.total.toString()
    }

    // 2. 获取我负责的待审批合同数（sceneType=1表示我负责的，auditStatus=0表示待审批）
    const contractStats = await getContractPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1,
      auditStatus: 0
    })
    if (contractStats && contractStats.total !== undefined) {
      projects[1].statValue = contractStats.total.toString()
    }

    // 3. 获取我负责的码单总数（sceneType=1表示我负责的）
    const yardListStats = await YardListApi.getYardListPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1
    })
    if (yardListStats && yardListStats.total !== undefined) {
      projects[2].statValue = yardListStats.total.toString()
    }

    // 4. 获取我负责的结算记录总数（sceneType=1表示我负责的）
    const settlementStats = await SettlementRecordApi.getSettlementRecordPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1
    })
    if (settlementStats && settlementStats.total !== undefined) {
      projects[3].statValue = settlementStats.total.toString()
    }

    // 5. 获取我负责的产品总数（产品管理已有权限控制，显示有权限的产品）
    const productStats = await getProductPage({ pageNo: 1, pageSize: 1 })
    if (productStats && productStats.total !== undefined) {
      projects[4].statValue = productStats.total.toString()
    }

    // 6. 获取我负责的本月新增客户数（sceneType=1表示我负责的）
    const currentDate = new Date()
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    const monthlyCustomerStats = await getCustomerPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1,
      createTime: [firstDayOfMonth, currentDate]
    })
    if (monthlyCustomerStats && monthlyCustomerStats.total !== undefined) {
      projects[5].statValue = monthlyCustomerStats.total.toString()
    }

  } catch (error) {
    console.warn('获取统计数据失败:', error)
    // 如果获取失败，保持默认值0
  }
}

// 获取我负责的合同列表
let myContracts = reactive([])
const getMyContracts = async () => {
  try {
    // 查询我负责的合同，按创建时间倒序，取前5条
    const data = await getContractPage({
      pageNo: 1,
      pageSize: 5,
      sceneType: 1 // 我负责的
    })

    const contracts = data.list || []

    // 获取客户名称
    for (const contract of contracts) {
      if (contract.customerId) {
        try {
          const customer = await getCustomer(contract.customerId)
          contract.customerName = customer.name || '未知客户'
        } catch (error) {
          console.error('获取客户名称失败:', error)
          contract.customerName = '未知客户'
        }
      } else {
        contract.customerName = '未知客户'
      }
    }

    myContracts.splice(0, myContracts.length, ...contracts)
  } catch (error) {
    console.error('获取我负责的合同失败:', error)
    myContracts.splice(0, myContracts.length)
  }
}

// 获取快捷入口
let shortcut = reactive<Shortcut[]>([])

const getShortcut = async () => {
  const data = [
    {
      name: '客户公海配置',
      icon: 'ep:setting',
      url: '/crm/config/customer-pool-config',
      color: '#409EFF'
    },
    {
      name: '客户限制配置',
      icon: 'ep:lock',
      url: '/crm/config/customer-limit-config',
      color: '#67C23A'
    },
    {
      name: '产品分类配置',
      icon: 'ep:collection-tag',
      url: '/crm/config/product/category',
      color: '#E6A23C'
    },
    {
      name: '商机状态配置',
      icon: 'ep:flag',
      url: '/crm/config/business-status',
      color: '#F56C6C'
    },
    {
      name: '合同配置',
      icon: 'ep:connection',
      url: '/crm/config/contract-config',
      color: '#1e44a5'
    },
    {
      name: '系统用户管理',
      icon: 'ep:user-filled',
      url: '/system/user',
      color: '#9C27B0'
    }
  ]
  shortcut = Object.assign(shortcut, data)
}

// 我的合同占比统计
const getMyContractStats = async () => {
  try {
    // 获取我负责的合同数量
    const ownerData = await getContractPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 1 // 我负责的
    })
    const ownerCount = ownerData.total || 0

    // 获取我参与的合同数量
    const involvedData = await getContractPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 2 // 我参与的
    })
    const involvedCount = involvedData.total || 0

    // 获取我下属负责的合同数量
    const subordinateData = await getContractPage({
      pageNo: 1,
      pageSize: 1,
      sceneType: 3 // 下属负责的
    })
    const subordinateCount = subordinateData.total || 0

    const data = [
      { value: ownerCount, name: '我负责的' },
      { value: involvedCount, name: '我参与的' },
      { value: subordinateCount, name: '下属负责的' }
    ]

    // 更新饼图配置
    set(pieOptionsData, 'title.text', '我的合同占比')
    set(pieOptionsData, 'title.left', 'center')
    set(pieOptionsData, 'title.top', '20px')

    // 更新图例
    set(pieOptionsData, 'legend.orient', 'vertical')
    set(pieOptionsData, 'legend.left', 'left')
    set(pieOptionsData, 'legend.top', 'middle')
    set(
      pieOptionsData,
      'legend.data',
      data.map((v) => v.name)
    )

    // 更新系列配置
    set(pieOptionsData, 'series.0.name', '合同数量')
    set(pieOptionsData, 'series.0.center', ['60%', '60%'])
    set(pieOptionsData, 'series.0.radius', '55%')

    // 更新工具提示格式
    set(pieOptionsData, 'tooltip.formatter', '{a} <br/>{b} : {c} ({d}%)')

    pieOptionsData!.series![0].data = data.map((v, index) => {
      const colors = ['#5470c6', '#91cc75', '#fac858'] // 蓝色、绿色、橙色
      return {
        name: v.name,
        value: v.value,
        itemStyle: {
          color: colors[index]
        }
      }
    })
  } catch (error) {
    console.error('获取合同统计失败:', error)
    // 如果获取失败，使用默认数据
    const defaultData = [
      { value: 0, name: '我负责的' },
      { value: 0, name: '我参与的' },
      { value: 0, name: '下属负责的' }
    ]

    set(pieOptionsData, 'title.text', '我的合同占比')
    set(pieOptionsData, 'title.left', 'center')
    set(pieOptionsData, 'title.top', '20px')

    set(pieOptionsData, 'legend.orient', 'vertical')
    set(pieOptionsData, 'legend.left', 'left')
    set(pieOptionsData, 'legend.top', 'middle')
    set(
      pieOptionsData,
      'legend.data',
      defaultData.map((v) => v.name)
    )

    set(pieOptionsData, 'series.0.name', '合同数量')
    set(pieOptionsData, 'series.0.center', ['60%', '60%'])
    set(pieOptionsData, 'tooltip.formatter', '{a} <br/>{b} : {c} ({d}%)')

    pieOptionsData!.series![0].data = defaultData.map((v, index) => {
      const colors = ['#5470c6', '#91cc75', '#fac858']
      return {
        name: v.name,
        value: v.value,
        itemStyle: {
          color: colors[index]
        }
      }
    })
  }
}
const barOptionsData = reactive<EChartsOption>(barOptions) as EChartsOption

// 我负责的合同金额统计
const getMyContractAmountStats = async () => {
  try {
    // 获取我负责的最近5个合同
    const contractData = await getContractPage({
      pageNo: 1,
      pageSize: 5,
      sceneType: 1 // 我负责的
    })

    const contracts = contractData.list || []

    // 按创建时间排序（最旧的在前，最新的在右边）
    contracts.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime())

    const data = contracts.map(contract => {
      // 格式化日期为 MM-DD 格式
      const date = new Date(contract.createTime)
      const formattedDate = `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`

      return {
        name: formattedDate,
        value: contract.totalPrice || 0,
        contractName: contract.name || '未知合同'
      }
    })

    // 如果合同数量不足5个，用空数据补充
    while (data.length < 5) {
      data.push({
        name: '--',
        value: 0,
        contractName: '暂无合同'
      })
    }

    // 更新柱状图配置
    set(barOptionsData, 'title.text', '我负责的合同金额（最近5个）')
    set(barOptionsData, 'title.left', 'center')
    set(barOptionsData, 'title.top', '20px')

    // 更新坐标轴
    set(barOptionsData, 'xAxis.data', data.map(v => v.name))
    set(barOptionsData, 'yAxis.name', '金额（CNY/USD）')
    set(barOptionsData, 'yAxis.nameTextStyle', { color: '#666' })

    // 更新工具提示
    set(barOptionsData, 'tooltip', {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params
        const dataItem = data[param.dataIndex]
        if (dataItem && dataItem.contractName) {
          return `${dataItem.contractName}<br/>日期: ${param.name}<br/>金额: ¥${param.value.toLocaleString()}`
        } else {
          return `日期: ${param.name}<br/>金额: ¥${param.value.toLocaleString()}`
        }
      }
    })

    // 更新系列数据
    set(barOptionsData, 'series', [
      {
        name: '合同金额',
        data: data.map(v => v.value),
        type: 'bar',
        itemStyle: {
          color: '#67C23A'
        },
        emphasis: {
          itemStyle: {
            color: '#85ce61',
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ])

  } catch (error) {
    console.error('获取合同金额统计失败:', error)

    // 如果获取失败，使用默认数据
    const defaultData = [
      { name: '--', value: 0 },
      { name: '--', value: 0 },
      { name: '--', value: 0 },
      { name: '--', value: 0 },
      { name: '--', value: 0 }
    ]

    set(barOptionsData, 'title.text', '我负责的合同金额（最近5个）')
    set(barOptionsData, 'title.left', 'center')
    set(barOptionsData, 'title.top', '20px')

    set(barOptionsData, 'xAxis.data', defaultData.map(v => v.name))
    set(barOptionsData, 'yAxis.name', '金额（CNY/USD）')
    set(barOptionsData, 'tooltip', {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const param = Array.isArray(params) ? params[0] : params
        return `暂无合同<br/>日期: ${param.name}<br/>金额: ¥${param.value.toLocaleString()}`
      }
    })

    set(barOptionsData, 'series', [
      {
        name: '合同金额',
        data: defaultData.map(v => v.value),
        type: 'bar',
        itemStyle: {
          color: '#67C23A'
        }
      }
    ])
  }
}

const getAllApi = async () => {
  await Promise.all([
    getProject(),
    getMyContracts(),
    getShortcut(),
    getMyContractStats(),
    getMyContractAmountStats()
  ])
  loading.value = false
}

const handleProjectClick = (message: string) => {
  router.push(message)
}

const handleShortcutClick = (url: string) => {
  router.push(url)
}

/** 打开合同详情 */
const openContractDetail = (contractId: number) => {
  if (contractId) {
    router.push({
      name: 'CrmContractDetail',
      params: { id: contractId }
    })
  }
}

getAllApi()
</script>
