import request from '@/config/axios'

// CRM结算单VO
export interface SettlementRecordVO {
  id?: number // 主键ID
  settlementDate: string // 结算日期
  contractNo?: string // 合同号
  productNameCode: string // 品名款号
  composition?: string // 成分
  specification?: string // 规格
  weight?: number // 克重
  width?: string // 幅宽
  quantity: number // 数量
  unit?: number // 单位
  unitPrice: number // 单价
  totalAmount: number // 金额
  remark?: string // 备注
  // 新增关联字段
  ownerUserId: number // 负责人用户编号
  ownerUserName?: string // 负责人名称
  customerId?: number // 客户编号
  customerName?: string // 客户名称
  contractId: number // 合同编号
  contractName?: string // 合同名称
}

// CRM结算单API
export const SettlementRecordApi = {
  // 查询CRM结算单分页
  getSettlementRecordPage: async (params: any) => {
    return await request.get({ url: `/crm/settlement-record/page`, params })
  },

  // 查询CRM结算单详情
  getSettlementRecord: async (id: number) => {
    return await request.get({ url: `/crm/settlement-record/get?id=` + id })
  },

  // 新增CRM结算单
  createSettlementRecord: async (data: SettlementRecordVO) => {
    return await request.post({ url: `/crm/settlement-record/create`, data })
  },

  // 修改CRM结算单
  updateSettlementRecord: async (data: SettlementRecordVO) => {
    return await request.put({ url: `/crm/settlement-record/update`, data })
  },

  // 删除CRM结算单
  deleteSettlementRecord: async (id: number) => {
    return await request.delete({ url: `/crm/settlement-record/delete?id=` + id })
  },

  // 导出CRM结算单Excel
  exportSettlementRecord: async (params) => {
    return await request.download({ url: `/crm/settlement-record/export-excel`, params })
  },
}
