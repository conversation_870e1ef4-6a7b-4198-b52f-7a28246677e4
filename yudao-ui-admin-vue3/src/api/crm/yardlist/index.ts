import request from '@/config/axios'

// CRM码单主 VO
export interface YardListVO {
  id: number // 主键ID
  contractId: number // 合同编号
  customerId: number // 客户编号
  ownerUserId: number // 负责人的用户编号
  deliveryDate: Date // 发货日期
  productName: string // 货品名称
  totalPieces: number // 总匹数
  totalMeters: number // 总米数
  recipient: string // 签收人
  unit: number // 单位
  remark: string // 备注
}

// CRM码单主 API
export const YardListApi = {
  // 查询CRM码单主分页
  getYardListPage: async (params: any) => {
    return await request.get({ url: `/crm/yard-list/page`, params })
  },

  // 查询CRM码单主详情
  getYardList: async (id: number) => {
    return await request.get({ url: `/crm/yard-list/get?id=` + id })
  },

  // 新增CRM码单主
  createYardList: async (data: YardListVO) => {
    return await request.post({ url: `/crm/yard-list/create`, data })
  },

  // 修改CRM码单主
  updateYardList: async (data: YardListVO) => {
    return await request.put({ url: `/crm/yard-list/update`, data })
  },

  // 删除CRM码单主
  deleteYardList: async (id: number) => {
    return await request.delete({ url: `/crm/yard-list/delete?id=` + id })
  },

  // 导出单个码单 Excel
  exportYardList: async (id: number) => {
    return await request.download({ url: `/crm/yard-list/export-excel/${id}` })
  },

// ==================== 子表（CRM码单子表（款号包号详情）） ====================

  // 获得CRM码单子表（款号包号详情）列表
  getYardListDetailListByYardListId: async (yardListId) => {
    return await request.get({ url: `/crm/yard-list/yard-list-detail/list-by-yard-list-id?yardListId=` + yardListId })
  },
}
