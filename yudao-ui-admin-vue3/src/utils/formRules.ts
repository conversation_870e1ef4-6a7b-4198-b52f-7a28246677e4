const { t } = useI18n()

// 必填项
export const required = {
  required: true,
  message: t('common.required')
}

// 国际手机号验证规则
export const mobileRule = {
  validator: (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback()
      return
    }

    // 国际手机号验证规则
    // 支持以下格式：
    // 1. 中国手机号：1xxxxxxxxxx
    // 2. 国际号码（带+号）：+1234567890, +86xxxxxxxxxx
    // 3. 国际号码（不带+号）：1234567890 (4-15位数字)
    const patterns = [
      /^1[3-9]\d{9}$/, // 中国手机号
      /^\+\d{1,3}\d{4,14}$/, // 国际号码（带+号）
      /^\d{4,15}$/ // 国际号码（不带+号，4-15位数字）
    ]

    const isValid = patterns.some(pattern => pattern.test(value))

    if (!isValid) {
      callback(new Error('请输入正确的手机号码（支持国内外手机号）'))
    } else {
      callback()
    }
  },
  trigger: 'blur'
}
